
// WutheringWaves登录服务 - 阿里云ESA边缘函数版本
// 基于官方API文档格式

// ESA缓存API实现 - 分布式缓存解决方案（带完整降级）
class ESACache {
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || 'https://cache.esa.aliyuncs.com';
    this.apiKey = options.apiKey || '';
    this.namespace = options.namespace || 'ww-login';
    this.timeout = options.timeout || 600; // 秒
    this.maxRetries = options.maxRetries || 2;
    this.enableESACache = !!this.apiKey; // 只有配置了API密钥才启用ESA缓存
  }

  async set(key, value, ttl = 600) {
    // 如果没有配置ESA缓存或明确禁用，直接使用内存缓存
    if (!this.enableESACache) {
      this.fallbackToMemoryCache(key, value, Date.now() + (ttl * 1000));
      return true;
    }

    const cacheKey = `${this.namespace}:${key}`;
    const expiry = Date.now() + (ttl * 1000);
    const data = {
      value: value,
      expiry: expiry,
      timestamp: Date.now()
    };

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const response = await fetch(`${this.baseUrl}/v1/cache/set`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          },
          body: JSON.stringify({
            key: cacheKey,
            value: JSON.stringify(data),
            ttl: ttl
          })
        });

        if (response.ok) {
          console.log(`ESA Cache SET: ${key} -> Success (attempt ${attempt})`);
          return true;
        } else {
          const errorText = await response.text();
          console.warn(`ESA Cache SET failed (${response.status}): ${errorText}`);
        }
      } catch (error) {
        console.warn(`ESA Cache SET failed (attempt ${attempt}):`, error.message);
      }
      
      if (attempt === this.maxRetries) {
        // 所有尝试都失败，降级到内存缓存
        console.log(`ESA Cache unavailable, using memory cache for: ${key}`);
        this.fallbackToMemoryCache(key, value, expiry);
      }
    }
    return false;
  }

  async get(key, defaultValue = null) {
    // 如果没有配置ESA缓存，直接使用内存缓存
    if (!this.enableESACache) {
      return this.fallbackFromMemoryCache(key, defaultValue);
    }

    const cacheKey = `${this.namespace}:${key}`;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const response = await fetch(`${this.baseUrl}/v1/cache/get?key=${encodeURIComponent(cacheKey)}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${this.apiKey}`
          }
        });

        if (response.ok) {
          const result = await response.json();
          if (result.value) {
            const data = JSON.parse(result.value);
            if (Date.now() < data.expiry) {
              console.log(`ESA Cache GET: ${key} -> Hit`);
              return data.value;
            } else {
              console.log(`ESA Cache GET: ${key} -> Expired`);
              // 过期数据自动删除
              this.delete(key);
              return defaultValue;
            }
          }
          console.log(`ESA Cache GET: ${key} -> Miss`);
          return defaultValue;
        } else {
          const errorText = await response.text();
          console.warn(`ESA Cache GET failed (${response.status}): ${errorText}`);
        }
      } catch (error) {
        console.warn(`ESA Cache GET failed (attempt ${attempt}):`, error.message);
      }
      
      if (attempt === this.maxRetries) {
        // 所有尝试都失败，降级到内存缓存
        console.log(`ESA Cache unavailable, using memory cache for: ${key}`);
        return this.fallbackFromMemoryCache(key, defaultValue);
      }
    }
    return defaultValue;
  }

  async delete(key) {
    // 如果没有配置ESA缓存，直接清理内存缓存
    if (!this.enableESACache) {
      this.cleanupMemoryCache(key);
      return true;
    }

    const cacheKey = `${this.namespace}:${key}`;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const response = await fetch(`${this.baseUrl}/v1/cache/delete?key=${encodeURIComponent(cacheKey)}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${this.apiKey}`
          }
        });

        if (response.ok) {
          console.log(`ESA Cache DELETE: ${key} -> Success`);
          // 同时清理内存缓存
          this.cleanupMemoryCache(key);
          return true;
        } else {
          const errorText = await response.text();
          console.warn(`ESA Cache DELETE failed (${response.status}): ${errorText}`);
        }
      } catch (error) {
        console.warn(`ESA Cache DELETE failed (attempt ${attempt}):`, error.message);
      }
      
      if (attempt === this.maxRetries) {
        // 清理内存缓存
        this.cleanupMemoryCache(key);
      }
    }
    return false;
  }

  // 降级到内存缓存
  fallbackToMemoryCache(key, value, expiry) {
    if (!this.memoryCache) {
      this.memoryCache = new Map();
    }
    this.memoryCache.set(key, { value, expiry });
    console.log(`Memory Cache: ${key} -> Stored`);
  }

  fallbackFromMemoryCache(key, defaultValue) {
    if (!this.memoryCache) return defaultValue;
    
    const item = this.memoryCache.get(key);
    if (item && Date.now() < item.expiry) {
      console.log(`Memory Cache: ${key} -> Hit`);
      return item.value;
    } else {
      if (item) {
        this.memoryCache.delete(key);
      }
      return defaultValue;
    }
  }

  cleanupMemoryCache(key) {
    if (this.memoryCache) {
      this.memoryCache.delete(key);
    }
  }

  // 获取缓存统计信息
  getStats() {
    return {
      esa: {
        available: this.enableESACache,
        baseUrl: this.baseUrl,
        namespace: this.namespace,
        configured: !!this.apiKey
      },
      memory: {
        size: this.memoryCache ? this.memoryCache.size : 0,
        keys: this.memoryCache ? Array.from(this.memoryCache.keys()) : []
      }
    };
  }
}

// 内存缓存降级实现 - 仅在ESA缓存不可用时使用
class MemoryCache {
  constructor(timeout = 600, maxSize = 1000) {
    this.cache = new Map();
    this.timeout = timeout;
    this.maxSize = maxSize;
  }

  set(key, value) {
    this.cleanup();
    const expiry = Date.now() + (this.timeout * 1000);
    this.cache.set(key, { value, expiry });
    if (this.cache.size > this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
  }

  get(key, defaultValue = null) {
    const item = this.cache.get(key);
    if (!item) return defaultValue;
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return defaultValue;
    }
    return item.value;
  }

  delete(key) {
    this.cache.delete(key);
  }

  cleanup() {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key);
      }
    }
  }
}

// 初始化ESA缓存实例
const globalCache = new ESACache({
  baseUrl: (typeof Deno !== 'undefined' ? Deno.env.get('ESA_CACHE_URL') : process.env.ESA_CACHE_URL) || 'https://cache.esa.aliyuncs.com',
  apiKey: (typeof Deno !== 'undefined' ? Deno.env.get('ESA_CACHE_KEY') : process.env.ESA_CACHE_KEY) || '',
  namespace: (typeof Deno !== 'undefined' ? Deno.env.get('ESA_CACHE_NAMESPACE') : process.env.ESA_CACHE_NAMESPACE) || 'ww-login',
  timeout: 600,
  maxRetries: 2
});

// 简单模板引擎
class SimpleTemplateEngine {
  constructor() {
    this.templates = new Map();
  }

  registerTemplate(name, content) {
    this.templates.set(name, content);
  }

  render(templateName, data = {}) {
    const template = this.templates.get(templateName);
    if (!template) {
      throw new Error('Template ' + templateName + ' not found');
    }

    let result = template;
    for (const [key, value] of Object.entries(data)) {
      const regex = new RegExp('{{\\\\s*' + key + '\\\\s*}}', 'g');
      result = result.replace(regex, value || '');
    }
    return result;
  }
}

// HTTP客户端 - 支持JSON和表单数据格式
class HTTPClient {
  async post(url, data, headers = {}) {
    try {
      // 检查Content-Type决定数据格式
      const contentType = headers['Content-Type'] || 'application/json';
      let body, finalHeaders = { ...headers };
      
      if (contentType.includes('application/x-www-form-urlencoded')) {
        // 表单数据格式
        const formData = new URLSearchParams();
        for (const [key, value] of Object.entries(data)) {
          formData.append(key, value);
        }
        body = formData;
        finalHeaders['Content-Type'] = 'application/x-www-form-urlencoded; charset=utf-8';
      } else {
        // JSON格式
        body = JSON.stringify(data);
        finalHeaders['Content-Type'] = 'application/json';
      }

      const response = await fetch(url, {
        method: 'POST',
        headers: finalHeaders,
        body: body,
      });

      if (!response.ok) {
        throw new Error('HTTP error! status: ' + response.status);
      }

      return await response.json();
    } catch (error) {
      console.error('HTTP request failed:', error);
      return null;
    }
  }
}

// 数据模型
class BaseModel {
  constructor(data = {}) {
    Object.assign(this, data);
  }
}

class LoginModel extends BaseModel {
  constructor(data = {}) {
    super({
      auth: data.auth || '',
      mobile: data.mobile || '',
      code: data.code || '',
      ck: data.ck || null,
      did: data.did || null,
      user_id: data.user_id || null,
      bot_id: data.bot_id || null,
    });
  }
}

class AuthModel extends BaseModel {
  constructor(data = {}) {
    super({
      bot_id: data.bot_id || '',
      user_id: data.user_id || '',
    });
    
    // 验证必填字段
    if (!this.bot_id || this.bot_id.trim() === '') {
      throw new Error('bot_id is required');
    }
    if (!this.user_id || this.user_id.trim() === '') {
      throw new Error('user_id is required');
    }
  }
}

class TokenModel extends BaseModel {
  constructor(data = {}) {
    super({
      token: data.token || '',
    });
  }
}

// Kuro API调用
class KuroAPI {
  constructor() {
    this.client = new HTTPClient();
  }

  async login(mobile, code) {
    const loginUrl = 'https://api.kurobbs.com/user/sdkLogin';
    const headers = {
      'source': 'ios',
      'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
      'devCode': '',
    };
    
    const did = this.generateUUID();
    const data = { mobile, code, devCode: did };

    try {
      const result = await this.client.post(loginUrl, data, headers);
      if (!result || result.code !== 200 || !result.data) {
        return [null, null];
      }
      return [result.data.token, did];
    } catch (error) {
      console.error('Kuro API login failed:', error);
      return [null, null];
    }
  }

  generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16).toUpperCase();
    });
  }
}

// 生成随机Token
function generateToken() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 注册模板
function registerTemplates(templateEngine) {
  const loginTemplate = '<!DOCTYPE html>\n' +
'<html lang="zh-CN">\n' +
'<head>\n' +
'    <meta charset="UTF-8">\n' +
'    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n' +
'    <title>WutheringWavesUID 登录</title>\n' +
'    <link rel="icon" href="https://web-static.kurobbs.com/resource/prod/icon.ico" type="image/x-icon">\n' +
'    <script src="https://static.geetest.com/v4/gt4.js"></script>\n' +
'    <script src="https://apps.bdimg.com/libs/jquery/1.6.4/jquery.min.js"></script>\n' +
'    <style>\n' +
'        body, html { margin: 0; padding: 0; height: 100%; font-family: \'Microsoft YaHei\', sans-serif; }\n' +
'        body { background-image: url(\'https://prod-alicdn-community.kurobbs.com/forum/465dcb694966492aba4d4982c47f0e8520240914.jpg\'); background-size: cover; background-position: center; background-repeat: no-repeat; display: flex; justify-content: center; align-items: center; }\n' +
'        body::before { content: ""; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-image: inherit; background-size: cover; background-position: center; background-repeat: no-repeat; filter: blur(8px); z-index: -1; }\n' +
'        .login-container { background-color: rgba(255, 255, 255, 0.2); padding: 2rem; border-radius: 16px; box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37); backdrop-filter: blur(8px); -webkit-backdrop-filter: blur(8px); border: 1px solid rgba(255, 255, 255, 0.18); width: 320px; transition: all 0.3s ease; }\n' +
'        .login-container:hover { transform: translateY(-5px); box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.45); }\n' +
'        .login-header { display: flex; align-items: center; margin-bottom: 2rem; justify-content: space-between; }\n' +
'        .banner { width: 80px; height: 80px; border-radius: 50%; overflow: hidden; border: 3px solid rgba(255, 255, 255, 0.5); box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); margin-right: 0.5rem; }\n' +
'        .banner img { width: 100%; height: 100%; object-fit: cover; }\n' +
'        .banner-text { flex: 1; padding-right: 1rem; margin-left: 0.5rem; }\n' +
'        .welcome-text { font-size: 1.5rem; font-weight: 700; color: #fff; margin-bottom: 0.25rem; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5); }\n' +
'        .logo-text { font-size: 0.9rem; font-weight: 700; color: #fff; margin-bottom: 0.25rem; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5); }\n' +
'        .kurobbs-text { font-size: 0.8rem; font-weight: 700; color: #e6e6e6; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5); }\n' +
'        .input-group { margin-bottom: 1.5rem; }\n' +
'        label { display: block; margin-bottom: 0.5rem; color: #fff; font-weight: 500; }\n' +
'        input[type="tel"], input[type="text"] { width: 100%; padding: 0.75rem; border: none; border-radius: 8px; box-sizing: border-box; font-size: 16px; background-color: rgba(255, 255, 255, 0.9); transition: all 0.3s ease; }\n' +
'        input[type="tel"]:focus, input[type="text"]:focus { outline: none; box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.6); }\n' +
'        .verification-group { display: flex; gap: 0.5rem; }\n' +
'        .verification-group input { flex-grow: 1; }\n' +
'        button { width: 100%; padding: 0.75rem; background-color: #4a90e2; color: white; border: none; border-radius: 8px; cursor: pointer; transition: all 0.3s ease; font-size: 16px; font-weight: 600; }\n' +
'        button:hover { background-color: #357abd; transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); }\n' +
'        button:disabled { background-color: #ccc; cursor: not-allowed; transform: none; box-shadow: none; }\n' +
'        #getCodeBtn { width: auto; white-space: nowrap; padding: 0.75rem 1rem; display: inline-block; }\n' +
'        .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); opacity: 0; transition: opacity 0.3s ease; }\n' +
'        .modal-content { background-color: #fefefe; margin: 15% auto; padding: 20px; border: 1px solid #888; width: 300px; border-radius: 10px; text-align: center; transform: scale(0.7); transition: transform 0.3s ease; }\n' +
'        .modal.show { display: block; opacity: 1; }\n' +
'        .modal.show .modal-content { transform: scale(1); }\n' +
'        .close { color: #aaa; float: right; font-size: 28px; font-weight: bold; cursor: pointer; }\n' +
'        .close:hover, .close:focus { color: #000; text-decoration: none; cursor: pointer; }\n' +
'        .user-id-display { text-align: right; color: rgba(255, 255, 255, 0.6); font-size: 0.8em; margin-bottom: 10px; transition: opacity 0.3s ease; }\n' +
'        .user-id-display:hover { opacity: 1; }\n' +
'        .countdown { font-size: 0.9em; color: #666; margin-top: 10px; }\n' +
'        .redirect-button { margin-top: 15px; padding: 10px 20px; background-color: #4a90e2; color: white; border: none; border-radius: 5px; cursor: pointer; transition: background-color 0.3s ease; }\n' +
'        .redirect-button:hover { background-color: #357abd; }\n' +
'    </style>\n' +
'</head>\n' +
'<body>\n' +
'    <div class="login-container">\n' +
'        <div class="login-header">\n' +
'            <div class="banner-text">\n' +
'                <div class="welcome-text">欢迎登录</div>\n' +
'                <div class="logo-text">WutheringWavesUID</div>\n' +
'                <div class="kurobbs-text">库街区</div>\n' +
'            </div>\n' +
'            <div class="banner">\n' +
'                <img src="https://web-static.kurobbs.com/adminConfig/27/role_icon/1727419309663.png" alt="Banner">\n' +
'            </div>\n' +
'        </div>\n' +
'        <form id="loginForm">\n' +
'            <div class="input-group">\n' +
'                <label for="phone">手机号</label>\n' +
'                <input type="tel" id="phone" name="phone" required pattern="[0-9]{11}" placeholder="请输入11位手机号">\n' +
'            </div>\n' +
'            <div class="input-group">\n' +
'                <label for="verificationCode">验证码</label>\n' +
'                <div class="verification-group">\n' +
'                    <input type="text" id="verificationCode" name="verificationCode" required placeholder="请输入验证码">\n' +
'                    <button type="button" id="getCodeBtn" disabled>获取验证码</button>\n' +
'                </div>\n' +
'            </div>\n' +
'            <div id="userIdDisplay" class="user-id-display">当前识别码: {{ userId }}</div>\n' +
'            <button type="submit" id="loginBtn">登录</button>\n' +
'        </form>\n' +
'    </div>\n' +
'    <div id="successModal" class="modal">\n' +
'        <div class="modal-content">\n' +
'            <span class="close">&times;</span>\n' +
'            <h2>登录成功</h2>\n' +
'            <p>欢迎回来！</p>\n' +
'            <p class="countdown">5秒后自动跳转到首页</p>\n' +
'            <button class="redirect-button">立即跳转</button>\n' +
'        </div>\n' +
'    </div>\n' +
'    <script>\n' +
'        const phoneInput = document.getElementById(\'phone\');\n' +
'        const verificationCodeInput = document.getElementById(\'verificationCode\');\n' +
'        const getCodeBtn = document.getElementById(\'getCodeBtn\');\n' +
'        const loginBtn = document.getElementById(\'loginBtn\');\n' +
'        const loginForm = document.getElementById(\'loginForm\');\n' +
'        phoneInput.addEventListener(\'input\', function () {\n' +
'            const isPhoneValid = phoneInput.value.length === 11;\n' +
'            const isCodeValid = verificationCodeInput.value.trim() !== "";\n' +
'            getCodeBtn.disabled = !isPhoneValid;\n' +
'            loginBtn.disabled = !isPhoneValid || !isCodeValid;\n' +
'        });\n' +
'        verificationCodeInput.addEventListener(\'input\', function () {\n' +
'            loginBtn.disabled = this.value === "" || phoneInput.value.length !== 11;\n' +
'        });\n' +
'        const captchaId = "ec4aa4174277d822d73f2442a165a2cd";\n' +
'        const product = "bind";\n' +
'        initGeetest4({\n' +
'            captchaId: captchaId,\n' +
'            product: product,\n' +
'        }, function (captcha) {\n' +
'            captcha.onReady(function () {}).onSuccess(function () {\n' +
'                var result = captcha.getValidate();\n' +
'                if (!result) { return alert(\'请完成验证\'); }\n' +
'                result.captcha_id = captchaId;\n' +
'                $.ajax({\n' +
'                    url: \'https://api.kurobbs.com/user/getSmsCodeForH5\',\n' +
'                    type: \'POST\',\n' +
'                    data: { "mobile": phoneInput.value, "geeTestData": JSON.stringify(result) },\n' +
'                    headers: { "devcode": generateRandomString(), "source": "h5" },\n' +
'                    success: res => alert(res.success ? \'验证码已发送\' : \'验证码发送失败 : \' + res.msg),\n' +
'                    error: () => alert(\'请求出错，请检查网络连接\')\n' +
'                });\n' +
'            }).onError(function () { alert(\'请求出错，请检查网络连接\'); });\n' +
'            getCodeBtn.onclick = function () {\n' +
'                this.disabled = true;\n' +
'                let countdown = 60;\n' +
'                const originalText = this.textContent;\n' +
'                captcha.showBox();\n' +
'                const timer = setInterval(() => {\n' +
'                    this.textContent = countdown + \'秒后重试\';\n' +
'                    countdown--;\n' +
'                    if (countdown < 0) {\n' +
'                        clearInterval(timer);\n' +
'                        this.textContent = originalText;\n' +
'                        this.disabled = false;\n' +
'                        captcha.hideBox();\n' +
'                    }\n' +
'                }, 1000);\n' +
'            }\n' +
'        });\n' +
'        loginForm.addEventListener(\'submit\', function (e) {\n' +
'            e.preventDefault();\n' +
'            const data = {\n' +
'                mobile: phoneInput.value,\n' +
'                code: verificationCodeInput.value,\n' +
'                auth: "{{ auth }}"\n' +
'            };\n' +
'            fetch(\'/waves/login\', {\n' +
'                method: \'POST\',\n' +
'                headers: { \'Content-Type\': \'application/json\' },\n' +

'                headers: { \'Content-Type: application/json\' },\n' +
'                body: JSON.stringify(data)\n' +
'            })\n' +
'            .then(response => response.json())\n' +
'            .then(res => {\n' +
'                if (res.success) { showSuccessModal(); }\n' +
'                else { alert(\'登录失败\' + (res.msg ? res.msg : \'，请重试\')); }\n' +
'            })\n' +
'            .catch(() => alert(\'登录失败，请重试\'));\n' +
'        });\n' +
'        function showSuccessModal() {\n' +
'            const modal = document.getElementById(\'successModal\');\n' +
'            const countdownElement = modal.querySelector(\'.countdown\');\n' +
'            const redirectButton = modal.querySelector(\'.redirect-button\');\n' +
'            let countdown = 5;\n' +
'            modal.classList.add(\'show\');\n' +
'            setTimeout(() => {\n' +
'                modal.querySelector(\'.modal-content\').style.transform = \'scale(1)\';\n' +
'                modal.querySelector(\'.modal-content\').style.opacity = \'1\';\n' +
'            }, 10);\n' +
'            function updateCountdown() {\n' +
'                countdownElement.textContent = countdown + \'秒后自动跳转到首页\';\n' +
'                if (countdown <= 0) {\n' +
'                    clearInterval(timer);\n' +
'                    window.location.href = \'/\';\n' +
'                }\n' +
'                countdown--;\n' +
'            }\n' +
'            const timer = setInterval(updateCountdown, 1000);\n' +
'            redirectButton.addEventListener(\'click\', () => {\n' +
'                clearInterval(timer);\n' +
'                window.location.href = \'/\';\n' +
'            });\n' +
'            modal.querySelector(\'.close\').addEventListener(\'click\', () => {\n' +
'                clearInterval(timer);\n' +
'                window.location.href = \'/\';\n' +
'            });\n' +
'        }\n' +
'        function generateRandomString() {\n' +
'            const chars = \'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\';\n' +
'            return Array(32).fill(0).map(() => chars.charAt(Math.floor(Math.random() * chars.length))).join(\'\');\n' +
'        }\n' +
'    </script>\n' +
'</body>\n' +
'</html>';

  const notFoundTemplate = '<!DOCTYPE html><html lang="zh-CN"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>页面未找到</title><style>body{display:flex;justify-content:center;align-items:center;height:100vh;margin:0;font-family:\'Microsoft YaHei\',sans-serif;background-color:#f5f5f5;}.error-container{text-align:center;padding:2rem;background-color:white;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}h1{color:#e74c3c;margin-bottom:1rem;}p{color:#666;margin-bottom:1rem;}a{color:#3498db;text-decoration:none;}a:hover{text-decoration:underline;}</style></head><body><div class="error-container"><h1>404 - 页面未找到</h1><p>抱歉，您访问的页面不存在。</p><a href="/">返回首页</a></div></body></html>';

  templateEngine.registerTemplate('index.html', loginTemplate);
  templateEngine.registerTemplate('404.html', notFoundTemplate);
}

// 路由处理函数
async function handleTokenGeneration(request, cache) {
  try {
    console.log('=== Token Generation Started ===');
    console.log('Request method:', request.method);
    console.log('Request URL:', request.url);
    
    const authData = await request.json();
    console.log('Auth data received:', authData);
    
    const auth = new AuthModel(authData);
    console.log('Auth model created:', { user_id: auth.user_id, bot_id: auth.bot_id });
    
    const token = generateToken();
    console.log('Token generated:', token);
    
    const loginModel = new LoginModel({
      auth: token,
      user_id: auth.user_id,
      bot_id: auth.bot_id
    });
    console.log('Login model created:', loginModel);
    
    cache.set(token, loginModel);
    console.log('Token cached with key:', token);
    console.log('=== Token Generation Completed ===');
    
    return new Response(JSON.stringify({ token }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  } catch (error) {
    console.error('Token generation error:', error);
    return new Response(JSON.stringify({ error: 'Invalid request', details: error.message }), {
      status: 400,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
}

async function handleLoginPage(token, cache, templateEngine) {
  try {
    const loginModel = cache.get(token);
    
    if (loginModel) {
      const html = templateEngine.render('index.html', {
        auth: token,
        userId: loginModel.user_id || 'Unknown'
      });
      
      return new Response(html, {
        status: 200,
        headers: {
          'Content-Type': 'text/html; charset=utf-8',
          'Access-Control-Allow-Origin': '*',
        },
      });
    } else {
      const html = templateEngine.render('404.html');
      return new Response(html, {
        status: 404,
        headers: {
          'Content-Type': 'text/html; charset=utf-8',
          'Access-Control-Allow-Origin': '*',
        },
      });
    }
  } catch (error) {
    return new Response('Internal Server Error', {
      status: 500,
      headers: {
        'Content-Type': 'text/plain',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
}

async function handleLogin(request, cache, kuroAPI) {
  try {
    const data = await request.json();
    const loginModel = new LoginModel(data);
    
    const temp = cache.get(loginModel.auth);
    if (!temp) {
      return new Response(JSON.stringify({ success: false, msg: '登录超时' }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      });
    }
    
    const [ck, did] = await kuroAPI.login(loginModel.mobile, loginModel.code);
    
    if (!ck) {
      return new Response(JSON.stringify({ success: false, msg: '验证码无效' }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      });
    }
    
    loginModel.ck = ck;
    loginModel.did = did;
    cache.set(loginModel.auth, loginModel);
    
    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  } catch (error) {
    return new Response(JSON.stringify({ success: false, msg: '服务器错误' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
}

async function handleGetLogin(request, cache) {
  try {
    const data = await request.json();
    const tokenModel = new TokenModel(data);
    
    const temp = cache.get(tokenModel.token);
    if (temp) {
      console.log('Found login data for token:', tokenModel.token);
      console.log('Login data:', { 
        ck: temp.ck ? 'present' : 'missing', 
        did: temp.did ? 'present' : 'missing',
        user_id: temp.user_id,
        bot_id: temp.bot_id 
      });
      
      if (temp.ck) {
        console.log('Login successful, deleting token and returning data');
        cache.delete(tokenModel.token);
      }
      
      // 返回完整的LoginModel数据，包含所有字段
      const result = {
        auth: temp.auth || tokenModel.token,
        mobile: temp.mobile || '',
        code: temp.code || '',
        ck: temp.ck || '',
        did: temp.did || '',
        user_id: temp.user_id || '',
        bot_id: temp.bot_id || ''
      };
      
      return new Response(JSON.stringify(result), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      });
    } else {
      console.log('No login data found for token:', tokenModel.token);
      return new Response(JSON.stringify(new LoginModel()), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      });
    }
  } catch (error) {
    console.error('Error in handleGetLogin:', error);
    return new Response(JSON.stringify(new LoginModel()), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
}

// ESA边缘函数主入口 - 基于官方文档格式
async function handleRequest(request, context) {
  try {
    const url = new URL(request.url);
    const path = url.pathname;
    const method = request.method;
    
    // 初始化组件 - 使用全局缓存确保持久化
    const templateEngine = new SimpleTemplateEngine();
    const kuroAPI = new KuroAPI();
    
    // 注册HTML模板
    registerTemplates(templateEngine);
    
    // 路由处理
    if (path === '/' || path === '/index.html') {
      // 根路径显示登录说明页面
      return new Response(generateWelcomePage(), {
        status: 200,
        headers: {
          'Content-Type': 'text/html; charset=utf-8',
          'Access-Control-Allow-Origin': '*',
        }
      });
    } else if (path === '/waves/token' && method === 'POST') {
      return await handleTokenGeneration(request, globalCache);
    } else if (path.startsWith('/waves/i/') && method === 'GET') {
      const token = path.split('/').pop();
      return await handleLoginPage(token, globalCache, templateEngine);
    } else if (path === '/waves/login' && method === 'POST') {
      return await handleLogin(request, globalCache, kuroAPI);
    } else if (path === '/waves/get' && method === 'POST') {
      return await handleGetLogin(request, globalCache);
    } else if (path === '/debug/cache' && method === 'GET') {
      // 调试端点 - 显示缓存中的所有token
      const cacheInfo = {
        cacheStats: globalCache.getStats(),
        timestamp: new Date().toISOString()
      };
      return new Response(JSON.stringify(cacheInfo, null, 2), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        }
      });
    } else {
      // 修复404页面中获取可用token的逻辑
      const cacheStats = globalCache.getStats();
      return new Response(generate404Page({
        path: path,
        method: method,
        availableTokens: cacheStats.memory.keys
      }), {
        status: 404,
        headers: {
          'Content-Type': 'text/html; charset=utf-8',
          'Access-Control-Allow-Origin': '*',
        }
      });
    }
  } catch (error) {
    console.error('Error processing request:', error);
    return new Response(JSON.stringify({
      error: 'Internal Server Error',
      message: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
}

// 导出处理程序 - ESA官方格式
export default {
  fetch: handleRequest
};