# ESA边缘函数部署配置
service: esa-ww-login

provider:
  name: aliyun
  region: cn-hangzhou
  runtime: nodejs18.x
  memorySize: 512
  timeout: 30
  environment:
    NODE_ENV: production

functions:
  index:
    handler: index.handler
    description: WutheringWaves登录服务
    events:
      - http:
          path: /{proxy+}
          method: ANY
          cors: true
      - http:
          path: /waves/token
          method: POST
          cors: true
      - http:
          path: /waves/i/{token}
          method: GET
          cors: true
      - http:
          path: /waves/login
          method: POST
          cors: true
      - http:
          path: /waves/get
          method: POST
          cors: true

plugins:
  - serverless-aliyun-function-compute

custom:
  serverless-aliyun-function-compute:
    accountId: ${env:ALIBABA_CLOUD_ACCOUNT_ID}
    region: ${env:ALIBABA_CLOUD_REGION, 'cn-hangzhou'}
    stage: ${opt:stage, 'dev'}

package:
  patterns:
    - index.js
    - '!node_modules/**'
    - '!test/**'
    - '!docs/**'