# 阿里云ESA边缘函数 - 快速部署指南

## 前置条件

1. **阿里云账号**: 确保有阿里云账号和ESA服务权限
2. **Node.js环境**: 确保安装Node.js 18+
3. **Serverless CLI**: 全局安装Serverless Framework

## 第一步：环境准备

```bash
# 1. 克隆或下载项目
# 如果是本地创建的项目，跳过此步

# 2. 进入项目目录
cd esa-ww-login

# 3. 安装依赖
npm install

# 4. 安装Serverless Framework
npm install -g serverless

# 5. 安装阿里云CLI
npm install -g @serverless-devs/s
```

## 第二步：配置阿里云凭据

```bash
# 配置阿里云AccessKey
s config add

# 或者手动设置环境变量
export ALIBABA_CLOUD_ACCOUNT_ID=your_account_id
export ALIBABA_CLOUD_REGION=cn-hangzhou
```

## 第三步：测试本地环境

```bash
# 语法检查
node -c index.js

# 验证package.json
cat package.json

# 验证serverless配置
cat serverless.yml
```

## 第四步：部署到开发环境

```bash
# 部署到开发环境
serverless deploy --stage dev

# 部署到生产环境
serverless deploy --stage prod
```

## 第五步：验证部署

部署完成后，查看输出信息：

```
Service Information:
service: esa-ww-login
stage: dev
region: cn-hangzhou
functions:
  index:
    description: 'WutheringWaves登录服务'
    url: https://your-function-url.aliyuncs.com/2016-08-15/proxy/esa-ww-login/index
```

## API测试

### 1. 测试Token生成
```bash
curl -X POST https://your-function-url/waves/token \
  -H "Content-Type: application/json" \
  -d '{"bot_id": "test_bot", "user_id": "test_user"}'
```

### 2. 测试登录页面
在浏览器中打开：
```
https://your-function-url/waves/i/YOUR_TOKEN
```

### 3. 测试登录接口
```bash
curl -X POST https://your-function-url/waves/login \
  -H "Content-Type: application/json" \
  -d '{"mobile": "13800138000", "code": "123456", "auth": "YOUR_TOKEN"}'
```

### 4. 测试获取登录结果
```bash
curl -X POST https://your-function-url/waves/get \
  -H "Content-Type: application/json" \
  -d '{"token": "YOUR_TOKEN"}'
```

## 故障排除

### 1. 部署失败
- 检查阿里云凭据配置
- 确认账户权限
- 检查网络连接

### 2. 函数超时
- 调整serverless.yml中的timeout值
- 优化函数逻辑
- 检查外部API响应时间

### 3. 内存不足
- 调整memorySize配置
- 优化代码内存使用
- 减少模板大小

## 性能优化建议

1. **冷启动优化**
   - 保持函数活跃
   - 使用 provisioned concurrency

2. **内存优化**
   - 调整缓存大小
   - 优化HTML模板
   - 减少变量生命周期

3. **响应时间优化**
   - 减少外部API调用
   - 优化HTML加载
   - 使用CDN加速

## 监控和日志

- 在阿里云控制台查看函数日志
- 设置CloudWatch告警
- 监控函数执行时间

## 清理和删除

```bash
# 删除函数
serverless remove

# 或者删除指定环境
serverless remove --stage dev
```

## 重要注意事项

1. **状态管理**: 边缘函数无状态，缓存仅在函数实例内有效
2. **超时限制**: 最大执行时间30秒
3. **内存限制**: 最大512MB
4. **请求大小**: 最大6MB
5. **并发限制**: 注意函数并发数限制

## 成本估算

基于阿里云ESA定价：
- 执行时间: 每次请求约1-3秒
- 内存使用: 50-100MB
- 月请求量估算成本
- 存储和传输成本

具体价格请参考阿里云官方定价页面。