# ESA边缘函数 - WutheringWaves登录服务

这是将原始Python FastAPI项目完全重写为JavaScript版本以适配阿里云ESA边缘函数的项目。

## 功能特性

- 库街区手机验证码登录
- 完全适配阿里云ESA边缘函数
- 无服务器架构，自动扩展
- 高性能边缘计算
- 内置内存缓存系统
- 完整的HTML界面

## 技术架构

- **语言**: JavaScript (ES6+)
- **运行时**: Node.js 18.x
- **平台**: 阿里云ESA边缘函数
- **部署**: Serverless Framework
- **缓存**: 内置内存缓存
- **HTTP客户端**: Fetch API
- **模板引擎**: 自定义简单模板引擎

## 项目结构

```
esa-ww-login/
├── index.js          # 主函数文件
├── package.json      # 项目配置
├── serverless.yml    # 部署配置
└── README.md         # 文档说明
```

## API端点

### 1. 生成Token
- **URL**: `POST /waves/token`
- **描述**: 生成认证token
- **参数**: 
  - `bot_id`: 机器人ID
  - `user_id`: 用户ID

### 2. 登录页面
- **URL**: `GET /waves/i/{token}`
- **描述**: 显示登录HTML页面
- **参数**: 
  - `token`: 认证token

### 3. 登录请求
- **URL**: `POST /waves/login`
- **描述**: 处理登录请求
- **参数**:
  - `mobile`: 手机号
  - `code`: 验证码
  - `auth`: 认证token

### 4. 获取登录结果
- **URL**: `POST /waves/get`
- **描述**: 获取登录结果
- **参数**:
  - `token`: 认证token

## 部署前准备

1. **安装Serverless Framework**:
```bash
npm install -g serverless
```

2. **安装阿里云CLI工具**:
```bash
npm install -g @serverless-devs/s
```

3. **配置阿里云凭据**:
```bash
s config add
```

## 部署命令

### 开发环境部署
```bash
cd esa-ww-login
npm install
serverless deploy --stage dev
```

### 生产环境部署
```bash
serverless deploy --stage prod
```

### 删除部署
```bash
serverless remove
```

## 环境变量

部署前需要设置以下环境变量：

```bash
export ALIBABA_CLOUD_ACCOUNT_ID=your_account_id
export ALIBABA_CLOUD_REGION=cn-hangzhou
```

## 性能优化

- **内存大小**: 512MB (可调整)
- **超时时间**: 30秒 (可调整)
- **缓存策略**: 10分钟TTL，最大1000条记录
- **冷启动优化**: 保持函数活跃

## 监控和日志

- 通过阿里云控制台查看函数日志
- 监控函数执行时间和内存使用
- 设置告警规则

## 限制说明

1. **函数限制**:
   - 最大执行时间: 30秒
   - 内存限制: 512MB
   - 请求大小限制: 6MB

2. **状态管理**:
   - 边缘函数无状态
   - 使用内存缓存 (10分钟过期)
   - 不支持持久化存储

3. **外部依赖**:
   - 仅使用Web原生API
   - 无第三方npm包依赖
   - 自定义实现所有功能

## 安全考虑

- CORS跨域支持
- 验证码验证
- Token超时机制
- 输入参数验证

## 故障排除

1. **部署失败**:
   - 检查阿里云凭据配置
   - 确认账户有足够权限
   - 检查网络连接

2. **函数超时**:
   - 增加timeout配置
   - 优化函数执行逻辑
   - 检查外部API响应时间

3. **内存不足**:
   - 增加memorySize配置
   - 优化缓存使用
   - 检查HTML模板大小

## 版本历史

- **v1.0.0**: 初始版本，完整移植Python功能
- 支持所有原始API端点
- 保持用户界面一致性
- 适配边缘函数环境

## 许可证

MIT License

## 技术支持

如有问题，请查看阿里云ESA官方文档或联系开发团队。