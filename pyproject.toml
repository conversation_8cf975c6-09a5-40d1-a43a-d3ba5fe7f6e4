[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "ww-login"
version = "0.1.1"
description = "Login service"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "black>=24.10.0",
    "fastapi>=0.115.6",
    "httpx>=0.28.1",
    "jinja2>=3.1.5",
    "pydantic>=2.10.5",
    "pydantic-settings>=2.7.1",
    "starlette>=0.41.3",
    "uvicorn>=0.34.0",
]

[project.scripts]
start = "ww_login.main:main"

[tool.setuptools]
packages = ["ww_login"]

[tool.uv]
package = true
