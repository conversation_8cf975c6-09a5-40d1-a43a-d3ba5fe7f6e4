#
# This file is autogenerated by pip-compile with Python 3.11
# by the following command:
#
#    pip-compile pyproject.toml
#
--index-url https://pypi.tuna.tsinghua.edu.cn/simple

annotated-types==0.7.0
    # via pydantic
anyio==4.8.0
    # via
    #   httpx
    #   starlette
black==25.1.0
    # via ww-login (pyproject.toml)
certifi==2025.1.31
    # via
    #   httpcore
    #   httpx
click==8.1.8
    # via
    #   black
    #   uvicorn
fastapi==0.115.8
    # via ww-login (pyproject.toml)
h11==0.14.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.7
    # via httpx
httpx==0.28.1
    # via ww-login (pyproject.toml)
idna==3.10
    # via
    #   anyio
    #   httpx
jinja2==3.1.5
    # via ww-login (pyproject.toml)
markupsafe==3.0.2
    # via jinja2
mypy-extensions==1.0.0
    # via black
packaging==24.2
    # via black
pathspec==0.12.1
    # via black
platformdirs==4.3.6
    # via black
pydantic==2.10.6
    # via
    #   fastapi
    #   pydantic-settings
    #   ww-login (pyproject.toml)
pydantic-core==2.27.2
    # via pydantic
pydantic-settings==2.8.0
    # via ww-login (pyproject.toml)
python-dotenv==1.0.1
    # via pydantic-settings
sniffio==1.3.1
    # via anyio
starlette==0.45.3
    # via
    #   fastapi
    #   ww-login (pyproject.toml)
typing-extensions==4.12.2
    # via
    #   anyio
    #   fastapi
    #   pydantic
    #   pydantic-core
uvicorn==0.34.0
    # via ww-login (pyproject.toml)
