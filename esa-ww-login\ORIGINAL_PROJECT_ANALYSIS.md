# WutheringWavesUID 登录服务 - 原项目详细功能分析

## 📁 项目结构

```
ww_login/
├── __init__.py
├── main.py              # 主应用程序
├── models.py            # 数据模型定义
├── kuro_api.py          # 库街区API调用
├── lcache.py            # 缓存系统
├── lsettings.py         # 配置管理
└── templates/           # HTML模板
    ├── index.html       # 登录页面模板
    └── 404.html         # 404错误页面
```

## 🏗️ 核心架构

### 1. 主应用程序 (main.py)
- **框架**: FastAPI
- **路由**: APIRouter with prefix="/waves"
- **模板引擎**: Jinja2
- **缓存**: 自定义TimedCache

#### 主要组件:
- `app`: FastAPI应用实例
- `waves_router`: 波浪相关路由
- `templates`: Jinja2环境
- `cache`: 全局缓存实例

### 2. 数据模型 (models.py)
使用Pydantic定义数据验证模型:

#### LoginModel:
```python
class LoginModel(BaseModel):
    auth: str = ""           # 认证token
    mobile: str = ""         # 手机号
    code: str = ""           # 验证码
    ck: Union[str, None] = ""    # 认证Cookie
    did: Union[str, None] = ""   # 设备ID
    user_id: Union[str, None] = ""  # 用户ID
    bot_id: Union[str, None] = ""   # 机器人ID
```

#### AuthModel:
```python
class AuthModel(BaseModel):
    bot_id: str              # 必填：机器人ID
    user_id: str             # 必填：用户ID
```

#### TokenModel:
```python
class TokenModel(BaseModel):
    token: str               # 认证token
```

## 🔄 核心业务流程

### 1. Token生成流程
**端点**: `POST /waves/token`

```python
@waves_router.post("/token", response_model=TokenModel, status_code=200)
async def generate_token(auth: AuthModel):
    _token = "".join(
        random.choice(string.ascii_letters + string.digits) for _ in range(8)
    )
    cache.set(_token, LoginModel(**auth.dict()))
    return {"token": _token}
```

**逻辑步骤**:
1. 接收AuthModel参数（bot_id, user_id）
2. 生成8位随机token
3. 创建LoginModel实例
4. 将token和LoginModel存储到缓存
5. 返回token

### 2. 登录页面显示
**端点**: `GET /waves/i/{token}`

```python
@waves_router.get("/i/{token}", response_class=HTMLResponse)
async def verify_html(token: str):
    m = cache.get(token)
    template_name = "index.html" if m else "404.html"
    template = templates.get_template(template_name)
    return HTMLResponse(
        template.render(auth=token, userId=m.user_id) if m else template.render()
    )
```

**逻辑步骤**:
1. 从URL提取token
2. 查找缓存中的LoginModel
3. 如果存在：渲染index.html模板，传递auth和userId
4. 如果不存在：渲染404.html模板

### 3. 登录处理
**端点**: `POST /waves/login`

```python
@waves_router.post("/login")
async def waves_login(data: LoginModel):
    temp = cache.get(data.auth)
    if temp is None:
        return {"success": False, "msg": "登录超时"}
    ck, did = await kuro_login(data.mobile, data.code)
    if not ck:
        return {"success": False, "msg": "验证码无效"}
    data.ck = ck
    data.did = did
    cache.set(data.auth, data)
    return {"success": True}
```

**逻辑步骤**:
1. 接收LoginModel数据
2. 验证token是否在缓存中
3. 调用库街区API进行登录验证
4. 如果成功：保存ck和did到LoginModel，更新缓存
5. 返回登录结果

### 4. 获取登录结果
**端点**: `POST /waves/get`

```python
@waves_router.post("/get", response_model=LoginModel, status_code=200)
async def waves_get_login(_token: TokenModel):
    temp = cache.get(_token.token, LoginModel())
    
    if temp and temp.ck:
        cache.delete(_token.token)
    
    return temp
```

**逻辑步骤**:
1. 接收TokenModel
2. 从缓存获取LoginModel
3. 如果有ck字段：删除缓存中的token（一次性使用）
4. 返回LoginModel数据

## 🔧 技术实现细节

### 1. 缓存系统 (lcache.py)
```python
class TimedCache:
    def __init__(self, timeout: int = 5, maxsize: int = 10):
        self.cache: OrderedDict[str, Tuple[Any, float]] = OrderedDict()
        self.timeout = timeout      # 默认5秒过期
        self.maxsize = maxsize      # 默认最大10条记录
```

**核心功能**:
- **自动过期**: 基于时间戳的过期机制
- **大小限制**: LRU策略删除最旧的记录
- **线程安全**: 使用OrderedDict保证顺序

**关键方法**:
- `set(key, value)`: 存储值，自动清理过期项
- `get(key, default)`: 获取值，检查过期时间
- `delete(key)`: 删除指定key
- `_clean_up()`: 清理所有过期项

### 2. 库街区API (kuro_api.py)
```python
async def kuro_login(mobile: str, code: str) -> Union[Optional[str], Optional[str]]:
    login_url = "https://api.kurobbs.com/user/sdkLogin"
    headers = {
        "source": "ios",
        "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
        "devCode": "",
    }
    did = str(uuid.uuid4()).upper()
    data = {"mobile": mobile, "code": code, "devCode": did}
```

**API特点**:
- 使用httpx异步HTTP客户端
- POST请求，表单数据格式
- 生成UUID作为设备ID
- 验证返回码和data字段
- 错误处理：网络异常、HTTP错误、JSON解析错误

### 3. 配置管理 (lsettings.py)
```python
class Settings(BaseSettings):
    host: str = "127.0.0.1"
    port: int = 7860
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
```

**功能**:
- 基于Pydantic Settings
- 支持环境变量
- 默认本地开发配置
- 支持.env文件

### 4. HTML模板系统
**登录页面 (index.html)**:
- 使用Jinja2模板引擎
- 动态渲染`{{ auth }}`和`{{ userId }}`
- 集成极客验证4.0
- 响应式设计，支持移动端
- 模态框成功提示

**404页面 (404.html)**:
- 简洁的404错误页面
- 提示"登录过期"
- 返回首页链接

## 🔐 安全机制

### 1. Token验证
- 8位随机字符串
- 缓存有效期控制
- 一次性使用机制（登录后删除）

### 2. 数据验证
- Pydantic模型验证
- 必填字段检查
- 类型安全保证

### 3. API安全
- HTTP-only Cookie标记
- CORS支持
- 请求限流（通过缓存限制）

## 🌐 外部依赖

### 运行时依赖:
- **FastAPI**: Web框架
- **pydantic**: 数据验证
- **jinja2**: 模板引擎
- **httpx**: HTTP客户端
- **uvicorn**: ASGI服务器
- **python-multipart**: 表单数据处理

### 开发依赖:
- **pytest**: 测试框架
- **black**: 代码格式化
- **isort**: 导入排序

## 📱 前端交互

### 1. 登录流程
1. 显示登录表单（手机号 + 验证码）
2. 极客验证4.0集成
3. 验证码获取（60秒冷却）
4. 表单提交到`/waves/login`
5. 成功模态框 + 自动跳转

### 2. 用户体验
- 背景图片：库街区社区背景
- 毛玻璃效果
- 悬停动画
- 响应式布局
- 自动图片加载

### 3. 验证机制
- 前端：手机号格式验证（11位数字）
- 前端：验证码长度检查
- 后端：模型验证
- 后端：缓存状态检查

## 🔄 工作流程图

```
用户请求 → FastAPI路由 → 数据验证 → 缓存检查 → 业务逻辑 → 外部API调用 → 响应
    ↓           ↓          ↓         ↓         ↓            ↓
1. Token生成 → Auth验证 → 创建模型 → 存储缓存 → 返回token
2. 登录页面 → Token验证 → 查找缓存 → 渲染模板 → 显示页面
3. 登录处理 → 数据验证 → 缓存检查 → API调用 → 更新缓存
4. 获取结果 → Token验证 → 查找缓存 → 删除缓存 → 返回数据
```

## 🚀 部署配置

### 1. 环境变量
```bash
HOST=0.0.0.0
PORT=7860
```

### 2. 启动方式
```python
if __name__ == "__main__":
    main()  # uvicorn.run(app, host=host, port=port)
```

### 3. 服务特性
- 异步处理
- 自动API文档（Swagger UI）
- 热重载支持
- 生产就绪配置

## 📊 性能特点

### 1. 缓存优化
- 内存缓存，快速访问
- 自动过期清理
- LRU淘汰策略

### 2. 异步处理
- FastAPI异步框架
- httpx异步HTTP客户端
- 非阻塞I/O操作

### 3. 资源管理
- 连接池复用
- 内存使用控制
- 自动垃圾回收

这个项目是一个完整的Web应用，实现了基于库街区API的用户认证系统，具有良好的架构设计和用户体验。