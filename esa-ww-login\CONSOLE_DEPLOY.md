# 阿里云ESA边缘函数 - 网页控制台部署指南

## 步骤一：准备工作文件
# 阿里云ESA边缘函数 - 网页控制台部署指南
## 📋 重要说明 - 基于官方API文档 + 404问题已解决
本项目已根据阿里云ESA官方API文档进行了最新优化，并解决了404访问问题：
- **导出格式**: `export default { fetch: handler }` (基于官方fetch API)
- **根路径支持**: 访问 "/" 显示欢迎页面，404错误已修复
- **函数入口**: 无需设置（自动识别）
- **运行时**: Node.js 18.15
- **无外部依赖**: 所有功能内联实现
- **语法验证**: ✅ 已通过检查

## 📋 重要说明
本项目已针对阿里云ESA边缘函数的格式要求进行了优化，包含：
- 符合ESA模块格式的默认导出对象
- 完整的内联实现，无外部依赖
- 优化的错误处理和响应格式
- 语法验证通过，可直接部署

## 🔧 项目文件说明
- `index.js` - 主函数文件（已修正ESA格式）
- `package.json` - 项目配置（可选）
- `serverless.yml` - 部署配置（命令行部署用）

1. **下载项目文件**：
   - 将 `index.js` 文件复制保存到本地
   - 获取项目的完整功能代码

2. **准备部署信息**：
   - 区域：建议选择 `cn-hangzhou`（杭州）
   - 服务名：`esa-ww-login`
## 📋 重要说明 - 基于官方API文档
本项目已根据阿里云ESA官方API文档进行了最新优化：
- **导出格式**: `export default { fetch: handler }` (基于官方fetch API)
- **函数入口**: 无需设置（自动识别）
- **运行时**: Node.js 18.15
- **无外部依赖**: 所有功能内联实现
- **语法验证**: ✅ 已通过检查
   - 函数名：`index`

## 步骤二：登录阿里云控制台

1. 打开浏览器，访问 [阿里云官网](https://www.aliyun.com)
2. 使用您的阿里云账号登录
3. 在控制台搜索框中输入 "边缘函数服务" 或 "Edge Scripting Service"
4. 点击进入ESA边缘函数服务控制台

## 步骤三：创建服务和函数

### 3.1 创建服务
1. 在ESA控制台左侧点击 **"服务管理"**
2. 点击 **"创建服务"** 按钮
3. 填写服务信息：
   - **服务名称**: `esa-ww-login`
   - **描述**: `WutheringWaves登录服务`
   - **区域**: 选择 `cn-hangzhou`
   - **授权方式**: 选择 **"新建角色"**
4. 点击 **"确定"** 创建服务

### 3.2 创建函数
1. 在创建的服务中，点击 **"创建函数"**
2. 选择 **"空白函数"** 模板
3. 填写函数基本信息：
   - **函数名称**: `index`
   - **描述**: `WutheringWaves登录服务主函数`
   - **运行时**: 选择 `Node.js 18.15`
4. 点击 **"下一步"**

## 步骤四：配置函数代码

### 4.1 粘贴代码
1. 在 "函数代码" 区域，选择 **"在线编辑"**
2. 清空编辑器中的默认代码
3. 复制 `index.js` 文件中的所有代码并粘贴到编辑器中
4. 确保代码格式正确，缩进完整

### 4.2 配置函数参数
- **函数入口**: 保持默认（无需手动设置，ESA会自动识别export default的fetch方法）
- **环境变量**: 
  ```
  NODE_ENV = production
  ```
- **初始化时间**: 设置为 `3` 秒
- **执行时间**: 设置为 `30` 秒
- **内存大小**: 设置为 `512` MB

> **重要说明**: 
> - 使用`export default { fetch }`格式，ESA自动识别
> - 代码使用标准Node.js ES6模块格式
> - 语法检查已通过，可直接部署
### 4.2 配置函数参数
- **函数入口**: 保持默认或设置为 `index.handler`
- **环境变量**: 
  ```
  NODE_ENV = production
  ```
- **初始化时间**: 设置为 `3` 秒
### 4.2 配置函数参数
- **函数入口**: 保持默认或设置为 `index.handler`
- **环境变量**: 
  ```
  NODE_ENV = production
  ```
- **初始化时间**: 设置为 `3` 秒
- **执行时间**: 设置为 `30` 秒
- **内存大小**: 设置为 `512` MB

> **重要说明**: 
> - 函数入口必须是 `index.handler`
> - 代码使用标准Node.js `module.exports` 导出格式
> - 语法检查已通过，可直接部署
- **执行时间**: 设置为 `30` 秒
- **内存大小**: 设置为 `512` MB

## 步骤五：配置触发器

### 5.1 HTTP触发器配置
1. 在 "触发器" 配置中，点击 **"添加触发器"**
2. 选择触发器类型：**"HTTP触发器"**
3. 配置HTTP触发器：
   - **触发器名称**: `http-trigger`
   - **认证方式**: 选择 **"无需认证"**（开发测试）或 **"阿里云专用"**（生产环境）
   - **请求方法**: 选择 **"ANY"**（支持所有方法）
   - **路径**: 设置为 `/.*`（支持所有路径）

### 5.2 特定路径配置
为了更好的路由管理，可以添加以下特定路径的触发器：

#### Token生成接口
- **触发器名称**: `token-trigger`
- **认证方式**: `无需认证`
- **请求方法**: `POST`
- **路径**: `/waves/token`

#### 登录页面接口
- **触发器名称**: `login-page-trigger`
- **认证方式**: `无需认证`
- **请求方法**: `GET`
- **路径**: `/waves/i/{token}`

#### 登录接口
- **触发器名称**: `login-trigger`
- **认证方式**: `无需认证`
- **请求方法**: `POST`
- **路径**: `/waves/login`

#### 获取结果接口
- **触发器名称**: `get-result-trigger`
- **认证方式**: `无需认证`
- **请求方法**: `POST`
- **路径**: `/waves/get`

## 步骤六：高级配置

### 6.1 并发配置
- **预留实例**: 设置为 `0-5`（根据负载需求）
- **最大实例**: 设置为 `100`（防止无限扩展）

### 6.2 日志配置
- **日志服务**: 保持默认或连接已有的日志服务
- **日志级别**: 设置为 `info`

### 6.3 监控配置
- 在函数列表中，点击函数名称进入详情
- 在 **"监控"** 页面可以查看：
  - 调用次数
  - 执行时间
  - 错误率
  - 内存使用

## 步骤七：部署和测试

### 7.1 部署函数
1. 完成所有配置后，点击 **"创建并部署"**
2. 等待部署完成（约1-3分钟）
3. 部署成功后，函数状态会显示为 **"运行中"**

### 7.2 获取访问地址
1. 在函数详情页，找到 **"触发器管理"** 标签
2. 点击HTTP触发器，查看访问地址
3. 记录形如这样的地址：
   ```
   https://123456789.cn-hangzhou.fc.aliyuncs.com/2016-08-15/proxy/esa-ww-login/index
   ```

## 步骤八：功能测试

### 8.1 测试Token生成
在浏览器或API测试工具中测试：
```bash
curl -X POST https://your-function-url/waves/token \
  -H "Content-Type: application/json" \
  -d '{"bot_id": "test_bot", "user_id": "test_user"}'
```

### 8.2 测试登录页面
在浏览器中访问：
```
https://your-function-url/waves/i/YOUR_TOKEN
```

## 步骤九：性能优化

### 9.1 缓存预热
- 在 **"版本管理"** 中创建新版本
- 设置 **"预热配置"** 保持函数活跃

### 9.2 监控告警
在控制台设置以下告警：
- 执行时间超过20秒
- 错误率超过5%
- 内存使用超过400MB

## 常见问题解决

### 1. 部署失败
- **原因**: 代码语法错误
- **解决**: 检查Node.js语法，确保所有括号正确匹配

### 2. 函数超时
- **原因**: 代码执行时间过长
- **解决**: 优化代码逻辑，分解复杂操作

### 3. 内存不足
- **原因**: HTML模板过大或缓存数据过多
- **解决**: 减小模板大小，优化缓存机制

### 4. 404错误
- **原因**: 触发器路径配置不正确
- **解决**: 重新检查触发器路径设置

## 生产环境建议

1. **安全性**:
   - 使用HTTPS
   - 设置访问控制
   - 配置API网关

2. **性能**:
   - 启用CDN加速
   - 设置合理的超时时间
   - 监控函数执行指标

3. **维护**:
   - 定期更新函数代码
   - 备份重要配置
   - 设置自动化监控

## 成本控制

在控制台中查看：
- **账单管理** - 查看具体费用
- **成本优化** - 设置预算告警
- **使用量统计** - 监控调用频次

通过以上步骤，您就可以完全通过阿里云网页控制台完成ESA边缘函数的部署，无需使用任何命令行工具。